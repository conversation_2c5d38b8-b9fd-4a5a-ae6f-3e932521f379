# CodeIgniter 4 URL Best Practices

## Always Use base_url() Function

In CodeIgniter 4 applications, **ALWAYS** use the `base_url()` function for all internal links and asset references. This ensures your application works correctly across different environments.

## ✅ Correct Usage Examples

### Navigation Links
```php
<!-- Brand/Logo link -->
<a href="<?= base_url() ?>">Home</a>

<!-- Internal page links -->
<a href="<?= base_url('organization') ?>">Organization Portal</a>
<a href="<?= base_url('products') ?>">Products</a>
<a href="<?= base_url('organization/register') ?>">Register</a>
```

### Asset Links
```php
<!-- CSS Files -->
<link href="<?= base_url('assets/css/wanspeen.css') ?>" rel="stylesheet">

<!-- JavaScript Files -->
<script src="<?= base_url('assets/js/app.js') ?>"></script>

<!-- Images -->
<img src="<?= base_url('assets/images/logo.png') ?>" alt="Logo">

<!-- Favicon -->
<link rel="icon" type="image/x-icon" href="<?= base_url('favicon.ico') ?>">
```

### Form Actions
```php
<!-- Form submissions -->
<form action="<?= base_url('organization/store') ?>" method="POST">
    <!-- form fields -->
</form>

<!-- AJAX endpoints -->
<script>
fetch('<?= base_url('api/v1/products') ?>')
    .then(response => response.json())
    .then(data => console.log(data));
</script>
```

### Redirects in Controllers
```php
// In Controllers
return redirect()->to(base_url());
return redirect()->to(base_url('organization/dashboard'));

// With route names
return redirect()->route('organization.dashboard');
```

## ❌ Incorrect Usage (Never Do This)

### Hardcoded URLs
```php
<!-- DON'T: Hardcoded localhost -->
<a href="http://localhost/wanspeen/organization">Organization</a>

<!-- DON'T: Relative paths without base_url -->
<a href="/organization">Organization</a>

<!-- DON'T: Hardcoded asset paths -->
<img src="/assets/images/logo.png" alt="Logo">
```

### Environment-Specific URLs
```php
<!-- DON'T: Environment-specific hardcoding -->
<a href="http://localhost:8080/products">Products</a>
<a href="https://wanspeen.com/products">Products</a>
```

## Environment Configuration

### Base URL Setup
Configure your base URL in `app/Config/App.php`:
```php
public string $baseURL = 'http://localhost/wanspeen/';
```

### Environment Variables (.env)
```env
# Development (CodeIgniter Spark)
# app.baseURL = 'http://localhost:8080/'

# XAMPP Testing (Current Active)
app.baseURL = 'http://localhost/wanspeen/'

# Production
# app.baseURL = 'https://wanspeen.com/'
```

## JavaScript Integration

### Pass Base URL to JavaScript
```php
<!-- In your view -->
<script>
    const baseURL = '<?= base_url() ?>';
    
    // Use in AJAX calls
    fetch(baseURL + 'api/v1/products')
        .then(response => response.json());
        
    // Use in redirects
    window.location.href = baseURL + 'organization';
</script>
```

### Dynamic JavaScript URLs
```php
<script>
    // Dynamic API calls
    function loadProducts() {
        return fetch('<?= base_url('api/v1/products') ?>')
            .then(response => response.json());
    }
    
    // Dynamic form submissions
    function submitForm(data) {
        return fetch('<?= base_url('organization/store') ?>', {
            method: 'POST',
            body: JSON.stringify(data),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
    }
</script>
```

## Route Configuration

### Define Clean Routes
```php
// app/Config/Routes.php
$routes->get('/', 'Home::index');
$routes->get('organization', 'Organization::index');
$routes->get('organization/register', 'Organization::register');
$routes->post('organization/store', 'Organization::store');

// API routes
$routes->group('api/v1', function($routes) {
    $routes->get('products', 'Api\ProductController::index');
    $routes->post('products', 'Api\ProductController::store');
});
```

### Named Routes (Recommended)
```php
// Define named routes
$routes->get('organization', 'Organization::index', ['as' => 'organization.index']);
$routes->get('organization/dashboard', 'Organization::dashboard', ['as' => 'organization.dashboard']);

// Use in views
<a href="<?= route_to('organization.index') ?>">Organization</a>
<a href="<?= route_to('organization.dashboard') ?>">Dashboard</a>

// Use in controllers
return redirect()->route('organization.dashboard');
```

## Benefits of Using base_url()

1. **Environment Flexibility**: Works across development, staging, and production
2. **Easy Deployment**: No URL changes needed when moving environments
3. **Maintenance**: Single point of configuration for base URL
4. **SSL Compatibility**: Automatically handles HTTP/HTTPS based on configuration
5. **Subdirectory Support**: Works when app is in subdirectories

## Common Mistakes to Avoid

1. **Mixing URL styles**: Don't mix `base_url()` with hardcoded URLs
2. **Forgetting asset URLs**: Always use `base_url()` for CSS, JS, and images
3. **AJAX endpoints**: Use `base_url()` for all API calls
4. **Form actions**: Never hardcode form action URLs
5. **JavaScript redirects**: Pass base URL to JavaScript for dynamic redirects

## Testing Across Environments

### Development Testing
```bash
# CodeIgniter Spark
php spark serve
# Access: http://localhost:8080/

# XAMPP
# Access: http://localhost/wanspeen/
```

### URL Validation Checklist
- [ ] All navigation links use `base_url()`
- [ ] All asset references use `base_url()`
- [ ] All form actions use `base_url()`
- [ ] All AJAX endpoints use `base_url()`
- [ ] JavaScript has access to base URL
- [ ] No hardcoded localhost URLs
- [ ] Works in multiple environments

## Remember
**In CodeIgniter 4, ALWAYS use `base_url()` for any internal URL reference. This is not optional - it's essential for a properly functioning application.**
