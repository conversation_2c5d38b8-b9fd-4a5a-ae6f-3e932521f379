# Wanspeen Tourism Product Information System
## Complete System Design Document

### Technology Stack
- **Backend Framework**: CodeIgniter 4 (Latest)
- **Frontend Framework**: Bootstrap 5.3.0
- **CSS Framework**: Bootstrap 5 with Custom CSS Variables
- **Icons**: Bootstrap Icons 1.10.0
- **Fonts**: Google Fonts (Poppins)
- **Database**: MySQL 8.0+ (MySQLi Driver)
- **Server Requirements**: PHP 8.1+, Apache/Nginx
- **Development Environment**: XAMPP
- **Package Manager**: Composer
- **Testing Framework**: PHPUnit 10.5.16+

---

## 1. System Architecture

### 1.1 Application Structure
```
wanspeen/
├── app/
│   ├── Controllers/
│   │   ├── Dakoii/          # Super Admin Controllers
│   │   ├── Organization/    # Organization Admin Controllers
│   │   ├── Public/          # Public Tourism Site Controllers
│   │   └── Auth/            # Authentication Controllers
│   ├── Models/              # Database Models
│   ├── Views/
│   │   ├── dakoii/          # Super Admin Views
│   │   ├── organization/    # Organization Admin Views
│   │   ├── public/          # Public Tourism Site Views
│   │   └── layouts/         # Common Layouts
│   ├── Libraries/           # Custom Libraries
│   ├── Helpers/             # Custom Helpers
│   └── Filters/             # Authentication & Authorization Filters
├── public/
│   ├── assets/
│   │   ├── css/
│   │   ├── js/
│   │   └── uploads/         # User uploaded files
│   └── index.php
└── writable/
    ├── logs/
    └── uploads/
```

### 1.2 URL Structure
**Note**: The application has been configured to remove `/public/index.php` from URLs for cleaner routing.

- **Public Site**: `https://wanspeen.com/` (Landing page with modern design)
- **Dakoii Portal**: `https://wanspeen.com/dakoii` (Super Admin Portal)
- **Organization Portal**: `https://wanspeen.com/organization` (Organization Admin Portal)
- **API Endpoints**: `https://wanspeen.com/api/v1/`

### 1.3 Frontend Design System
**Color Palette:**
- **Primary Green**: #28a745 (Main brand color)
- **Primary Orange**: #ffa500 (Accent color from logo)
- **Primary Maroon**: #961203 (Secondary brand color)
- **Light variants**: Used for backgrounds and subtle elements
- **Typography**: Poppins font family for modern, clean appearance

**Design Principles:**
- Modern, professional appearance
- Mobile-first responsive design
- Accessibility-focused
- Consistent color scheme across all portals
- Bootstrap 5 component library with custom styling

---

## 2. Current Implementation Status

### 2.1 Completed Features
- ✅ **URL Structure Optimization**: Removed `/public/index.php` from URLs
- ✅ **Landing Page**: Modern, responsive design with brand colors
- ✅ **CodeIgniter 4 Setup**: Latest framework with proper configuration
- ✅ **Bootstrap 5 Integration**: Modern UI framework with custom styling
- ✅ **Responsive Design**: Mobile-first approach with professional appearance
- ✅ **Brand Identity**: Consistent color scheme (Green, Orange, Maroon)

### 2.2 Directory Structure (Updated)
```
wanspeen/
├── index.php                   # Main entry point (moved from public/)
├── app/
│   ├── Controllers/
│   │   ├── Home.php            # Landing page controller
│   │   ├── Dakoii/            # Super Admin Controllers
│   │   ├── Organization/      # Organization Admin Controllers
│   │   └── Auth/              # Authentication Controllers
│   ├── Models/                # Database Models
│   ├── Views/
│   │   ├── public/            # Public site views
│   │   │   └── landing.php    # Modern landing page
│   │   ├── dakoii/            # Super Admin Views
│   │   ├── organization/      # Organization Admin Views
│   │   └── layouts/           # Common Layouts
│   ├── Libraries/             # Custom Libraries
│   ├── Helpers/               # Custom Helpers
│   └── Filters/               # Authentication & Authorization Filters
├── public/
│   ├── assets/
│   │   ├── css/               # Custom stylesheets
│   │   ├── js/                # Custom JavaScript
│   │   └── images/            # Static images
│   ├── favicon.ico
│   └── robots.txt
└── vendor/                    # Composer dependencies
```

### 2.3 Technology Dependencies
```json
{
    "require": {
        "php": "^8.1",
        "codeigniter4/framework": "^4.0"
    },
    "require-dev": {
        "fakerphp/faker": "^1.9",
        "mikey179/vfsstream": "^1.6",
        "phpunit/phpunit": "^10.5.16"
    }
}
```

---

## 3. Database Design

### 3.1 Complete Database Schema

```sql
-- =============================================
-- AUTHENTICATION & USER MANAGEMENT TABLES
-- =============================================

-- Dakoii Portal Users (Super Admins)
CREATE TABLE dakoii_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    is_active TINYINT(1) DEFAULT 1,
    last_login DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_active (is_active)
);

-- Organizations
CREATE TABLE organizations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    state_province VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Papua New Guinea',
    postal_code VARCHAR(20),
    website VARCHAR(200),
    logo_path VARCHAR(500),
    banner_path VARCHAR(500),
    is_active TINYINT(1) DEFAULT 1,
    subscription_type ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
    subscription_expires_at DATE,
    max_users INT DEFAULT 5,
    max_smes INT DEFAULT 50,
    max_products INT DEFAULT 200,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    INDEX idx_code (code),
    INDEX idx_name (name),
    INDEX idx_active (is_active),
    FOREIGN KEY (created_by) REFERENCES dakoii_users(id),
    FOREIGN KEY (updated_by) REFERENCES dakoii_users(id)
);

-- Organization Users (Organization Admins)
CREATE TABLE org_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    organization_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    role ENUM('admin', 'manager', 'editor') DEFAULT 'editor',
    permissions JSON, -- Store specific permissions
    is_active TINYINT(1) DEFAULT 1,
    last_login DATETIME NULL,
    email_verified_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    UNIQUE KEY unique_org_username (organization_id, username),
    UNIQUE KEY unique_org_email (organization_id, email),
    INDEX idx_organization (organization_id),
    INDEX idx_role (role),
    INDEX idx_active (is_active),
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES org_users(id),
    FOREIGN KEY (updated_by) REFERENCES org_users(id)
);

-- =============================================
-- PRODUCT MANAGEMENT TABLES
-- =============================================

-- Product Types (Tourism Categories)
CREATE TABLE product_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INT NULL, -- For hierarchical categories
    icon_class VARCHAR(100), -- CSS icon class
    color_code VARCHAR(7), -- Hex color code
    display_order INT DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    seo_title VARCHAR(200),
    seo_description TEXT,
    seo_keywords VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    INDEX idx_code (code),
    INDEX idx_parent (parent_id),
    INDEX idx_active (is_active),
    INDEX idx_display_order (display_order),
    FOREIGN KEY (parent_id) REFERENCES product_types(id),
    FOREIGN KEY (created_by) REFERENCES dakoii_users(id),
    FOREIGN KEY (updated_by) REFERENCES dakoii_users(id)
);

-- SMEs (Small & Medium Enterprises)
CREATE TABLE smes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    organization_id INT NOT NULL,
    code VARCHAR(50) NOT NULL,
    business_name VARCHAR(200) NOT NULL,
    business_type ENUM('accommodation', 'restaurant', 'tour_operator', 'transport', 'attraction', 'retail', 'other') NOT NULL,
    description TEXT,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    website VARCHAR(200),
    facebook_url VARCHAR(200),
    instagram_url VARCHAR(200),
    address TEXT,
    city VARCHAR(100),
    state_province VARCHAR(100),
    postal_code VARCHAR(20),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    business_hours JSON, -- Store opening hours
    license_number VARCHAR(100),
    tax_number VARCHAR(100),
    logo_path VARCHAR(500),
    banner_path VARCHAR(500),
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    is_verified TINYINT(1) DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    featured TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    UNIQUE KEY unique_org_code (organization_id, code),
    INDEX idx_organization (organization_id),
    INDEX idx_business_type (business_type),
    INDEX idx_city (city),
    INDEX idx_active (is_active),
    INDEX idx_featured (featured),
    INDEX idx_verified (is_verified),
    INDEX idx_rating (rating),
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES org_users(id),
    FOREIGN KEY (updated_by) REFERENCES org_users(id)
);

-- Products (Tourism Products)
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    organization_id INT NOT NULL,
    sme_id INT NOT NULL,
    product_type_id INT NOT NULL,
    code VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    short_description VARCHAR(500),
    full_description TEXT,
    highlights JSON, -- Array of highlight points
    inclusions JSON, -- What's included
    exclusions JSON, -- What's not included
    requirements TEXT, -- Requirements/restrictions
    cancellation_policy TEXT,
    price_adult DECIMAL(10,2),
    price_child DECIMAL(10,2),
    price_infant DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'PGK',
    duration_days INT DEFAULT 1,
    duration_hours INT DEFAULT 0,
    max_participants INT,
    min_participants INT DEFAULT 1,
    difficulty_level ENUM('easy', 'moderate', 'challenging', 'extreme') DEFAULT 'easy',
    physical_rating ENUM('low', 'moderate', 'high') DEFAULT 'low',
    age_restriction_min INT DEFAULT 0,
    age_restriction_max INT DEFAULT 120,
    seasonal_availability JSON, -- Available months/seasons
    location VARCHAR(200),
    meeting_point TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    tags JSON, -- Search tags
    featured_image_path VARCHAR(500),
    gallery_paths JSON, -- Array of image paths
    video_url VARCHAR(500),
    virtual_tour_url VARCHAR(500),
    booking_url VARCHAR(500),
    booking_phone VARCHAR(20),
    booking_email VARCHAR(100),
    status ENUM('draft', 'pending_review', 'published', 'suspended', 'archived') DEFAULT 'draft',
    published_at DATETIME NULL,
    view_count INT DEFAULT 0,
    booking_count INT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    is_featured TINYINT(1) DEFAULT 0,
    seo_title VARCHAR(200),
    seo_description TEXT,
    seo_keywords VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    UNIQUE KEY unique_org_code (organization_id, code),
    INDEX idx_organization (organization_id),
    INDEX idx_sme (sme_id),
    INDEX idx_product_type (product_type_id),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_rating (rating),
    INDEX idx_price (price_adult),
    INDEX idx_location (location),
    INDEX idx_published (published_at),
    FULLTEXT idx_search (name, short_description, full_description, tags),
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (sme_id) REFERENCES smes(id) ON DELETE CASCADE,
    FOREIGN KEY (product_type_id) REFERENCES product_types(id),
    FOREIGN KEY (created_by) REFERENCES org_users(id),
    FOREIGN KEY (updated_by) REFERENCES org_users(id)
);

-- Product Files (Images, Documents, etc.)
CREATE TABLE product_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    file_type ENUM('image', 'video', 'document', 'audio') NOT NULL,
    file_category ENUM('gallery', 'thumbnail', 'banner', 'brochure', 'menu', 'map', 'other') NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT, -- in bytes
    mime_type VARCHAR(100),
    alt_text VARCHAR(200),
    caption TEXT,
    display_order INT DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT,
    
    INDEX idx_product (product_id),
    INDEX idx_type_category (file_type, file_category),
    INDEX idx_active (is_active),
    INDEX idx_display_order (display_order),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES org_users(id)
);

-- =============================================
-- EVENTS MANAGEMENT TABLES
-- =============================================

-- Events (Tourism Events)
CREATE TABLE events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    organization_id INT NOT NULL,
    sme_id INT NULL, -- Events can be organized by SMEs or organizations
    code VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    short_description VARCHAR(500),
    full_description TEXT,
    event_type ENUM('festival', 'cultural', 'sports', 'music', 'food', 'art', 'business', 'religious', 'other') NOT NULL,
    category ENUM('free', 'paid', 'registration_required') DEFAULT 'free',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    timezone VARCHAR(50) DEFAULT 'Pacific/Port_Moresby',
    is_recurring TINYINT(1) DEFAULT 0,
    recurrence_pattern JSON, -- For recurring events
    venue_name VARCHAR(200),
    venue_address TEXT,
    venue_city VARCHAR(100),
    venue_capacity INT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    ticket_price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'PGK',
    booking_url VARCHAR(500),
    booking_phone VARCHAR(20),
    booking_email VARCHAR(100),
    registration_deadline DATE,
    age_restriction VARCHAR(100),
    dress_code VARCHAR(200),
    parking_info TEXT,
    accessibility_info TEXT,
    weather_dependency TINYINT(1) DEFAULT 0,
    cancellation_policy TEXT,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    website VARCHAR(200),
    facebook_event_url VARCHAR(500),
    instagram_url VARCHAR(200),
    featured_image_path VARCHAR(500),
    gallery_paths JSON,
    tags JSON,
    status ENUM('draft', 'published', 'cancelled', 'completed', 'archived') DEFAULT 'draft',
    published_at DATETIME NULL,
    attendee_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_featured TINYINT(1) DEFAULT 0,
    seo_title VARCHAR(200),
    seo_description TEXT,
    seo_keywords VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    UNIQUE KEY unique_org_code (organization_id, code),
    INDEX idx_organization (organization_id),
    INDEX idx_sme (sme_id),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_type (event_type),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_city (venue_city),
    FULLTEXT idx_search (name, short_description, full_description),
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (sme_id) REFERENCES smes(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES org_users(id),
    FOREIGN KEY (updated_by) REFERENCES org_users(id)
);

-- =============================================
-- SYSTEM TABLES
-- =============================================

-- Activity Log
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_type ENUM('dakoii', 'organization') NOT NULL,
    user_id INT NOT NULL,
    organization_id INT NULL,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT NOT NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user (user_type, user_id),
    INDEX idx_organization (organization_id),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_action (action),
    INDEX idx_created (created_at)
);

-- Settings
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category VARCHAR(50) NOT NULL,
    key_name VARCHAR(100) NOT NULL,
    value TEXT,
    description TEXT,
    is_public TINYINT(1) DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT,
    
    UNIQUE KEY unique_category_key (category, key_name),
    INDEX idx_category (category),
    INDEX idx_public (is_public)
);

-- File Uploads Tracking
CREATE TABLE file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT NOT NULL,
    file_category VARCHAR(50),
    original_name VARCHAR(255) NOT NULL,
    stored_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    uploaded_by_type ENUM('dakoii', 'organization') NOT NULL,
    uploaded_by_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_uploaded_by (uploaded_by_type, uploaded_by_id),
    INDEX idx_created (created_at)
);
```

---

## 4. CodeIgniter 4 Implementation Structure

### 4.1 Models

```php
// app/Models/BaseModel.php
<?php
namespace App\Models;
use CodeIgniter\Model;

class BaseModel extends Model
{
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected function setCreatedBy($data)
    {
        if (isset($data['data']['created_by'])) {
            return $data;
        }
        
        $session = session();
        if ($session->has('user_id')) {
            $data['data']['created_by'] = $session->get('user_id');
        }
        
        return $data;
    }
    
    protected function setUpdatedBy($data)
    {
        $session = session();
        if ($session->has('user_id')) {
            $data['data']['updated_by'] = $session->get('user_id');
        }
        
        return $data;
    }
}

// app/Models/DakoiiUserModel.php
<?php
namespace App\Models;

class DakoiiUserModel extends BaseModel
{
    protected $table = 'dakoii_users';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'username', 'email', 'password_hash', 'first_name', 'last_name',
        'phone', 'is_active', 'last_login', 'created_by', 'updated_by'
    ];
    
    protected $beforeInsert = ['setCreatedBy', 'hashPassword'];
    protected $beforeUpdate = ['setUpdatedBy', 'hashPassword'];
    
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password_hash'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
            unset($data['data']['password']);
        }
        return $data;
    }
    
    public function findByUsername($username)
    {
        return $this->where('username', $username)->where('is_active', 1)->first();
    }
}

// app/Models/OrganizationModel.php
<?php
namespace App\Models;

class OrganizationModel extends BaseModel
{
    protected $table = 'organizations';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'code', 'name', 'description', 'contact_person', 'email', 'phone',
        'address', 'city', 'state_province', 'country', 'postal_code',
        'website', 'logo_path', 'banner_path', 'is_active', 'subscription_type',
        'subscription_expires_at', 'max_users', 'max_smes', 'max_products',
        'created_by', 'updated_by'
    ];
    
    protected $beforeInsert = ['setCreatedBy', 'generateCode'];
    protected $beforeUpdate = ['setUpdatedBy'];
    
    protected function generateCode(array $data)
    {
        if (!isset($data['data']['code'])) {
            $data['data']['code'] = 'ORG' . strtoupper(substr(uniqid(), -6));
        }
        return $data;
    }
    
    public function getActiveOrganizations()
    {
        return $this->where('is_active', 1)->findAll();
    }
    
    public function getOrganizationStats($orgId)
    {
        $builder = $this->db->table('organizations o');
        $builder->select('o.*, 
            (SELECT COUNT(*) FROM org_users ou WHERE ou.organization_id = o.id AND ou.is_active = 1) as user_count,
            (SELECT COUNT(*) FROM smes s WHERE s.organization_id = o.id AND s.is_active = 1) as sme_count,
            (SELECT COUNT(*) FROM products p WHERE p.organization_id = o.id AND p.status = "published") as product_count,
            (SELECT COUNT(*) FROM events e WHERE e.organization_id = o.id AND e.status = "published") as event_count');
        $builder->where('o.id', $orgId);
        return $builder->get()->getRow();
    }
}
```

### 4.2 Controllers Structure

```php
// app/Controllers/BaseController.php
<?php
namespace App\Controllers;
use CodeIgniter\Controller;

abstract class BaseController extends Controller
{
    protected $helpers = ['url', 'form', 'filesystem'];
    protected $session;
    
    public function initController(\CodeIgniter\HTTP\RequestInterface $request, \CodeIgniter\HTTP\ResponseInterface $response, \Psr\Log\LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);
        $this->session = \Config\Services::session();
    }
    
    protected function logActivity($action, $entityType, $entityId, $oldValues = null, $newValues = null)
    {
        $activityLog = new \App\Models\ActivityLogModel();
        $activityLog->insert([
            'user_type' => $this->session->get('user_type'),
            'user_id' => $this->session->get('user_id'),
            'organization_id' => $this->session->get('organization_id'),
            'action' => $action,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'old_values' => $oldValues ? json_encode($oldValues) : null,
            'new_values' => $newValues ? json_encode($newValues) : null,
            'ip_address' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString()
        ]);
    }
}

// app/Controllers/Dakoii/Dashboard.php
<?php
namespace App\Controllers\Dakoii;
use App\Controllers\BaseController;

class Dashboard extends BaseController
{
    public function index()
    {
        $organizationModel = new \App\Models\OrganizationModel();
        $dakoiiUserModel = new \App\Models\DakoiiUserModel();
        $productTypeModel = new \App\Models\ProductTypeModel();
        
        $data = [
            'title' => 'Dakoii Dashboard',
            'total_organizations' => $organizationModel->where('is_active', 1)->countAllResults(),
            'total_admins' => $dakoiiUserModel->where('is_active', 1)->countAllResults(),
            'total_product_types' => $productTypeModel->where('is_active', 1)->countAllResults(),
            'recent_organizations' => $organizationModel->orderBy('created_at', 'DESC')->limit(5)->findAll()
        ];
        
        return view('dakoii/dashboard', $data);
    }
}

// app/Controllers/Organization/Dashboard.php
<?php
namespace App\Controllers\Organization;
use App\Controllers\BaseController;

class Dashboard extends BaseController
{
    public function index()
    {
        $orgId = $this->session->get('organization_id');
        
        $smeModel = new \App\Models\SmeModel();
        $productModel = new \App\Models\ProductModel();
        $eventModel = new \App\Models\EventModel();
        
        $data = [
            'title' => 'Organization Dashboard',
            'total_smes' => $smeModel->where('organization_id', $orgId)->where('is_active', 1)->countAllResults(),
            'total_products' => $productModel->where('organization_id', $orgId)->where('status', 'published')->countAllResults(),
            'total_events' => $eventModel->where('organization_id', $orgId)->where('status', 'published')->countAllResults(),
            'recent_products' => $productModel->where('organization_id', $orgId)->orderBy('created_at', 'DESC')->limit(5)->findAll()
        ];
        
        return view('organization/dashboard', $data);
    }
}
```

### 4.3 Authentication System

```php
// app/Libraries/AuthLibrary.php
<?php
namespace App\Libraries;

class AuthLibrary
{
    protected $session;
    
    public function __construct()
    {
        $this->session = \Config\Services::session();
    }
    
    public function loginDakoiiUser($username, $password)
    {
        $userModel = new \App\Models\DakoiiUserModel();
        $user = $userModel->findByUsername($username);
        
        if ($user && password_verify($password, $user->password_hash)) {
            $sessionData = [
                'user_id' => $user->id,
                'user_type' => 'dakoii',
                'username' => $user->username,
                'full_name' => $user->first_name . ' ' . $user->last_name,
                'is_logged_in' => true
            ];
            
            $this->session->set($sessionData);
            
            // Update last login
            $userModel->update($user->id, ['last_login' => date('Y-m-d H:i:s')]);
            
            return true;
        }
        
        return false;
    }
    
    public function loginOrgUser($username, $password, $organizationId = null)
    {
        $userModel = new \App\Models\OrgUserModel();
        $user = $userModel->findByUsernameAndOrg($username, $organizationId);
        
        if ($user && password_verify($password, $user->password_hash)) {
            $sessionData = [
                'user_id' => $user->id,
                'user_type' => 'organization',
                'organization_id' => $user->organization_id,
                'username' => $user->username,
                'full_name' => $user->first_name . ' ' . $user->last_name,
                'role' => $user->role,
                'permissions' => json_decode($user->permissions, true),
                'is_logged_in' => true
            ];
            
            $this->session->set($sessionData);
            
            // Update last login
            $userModel->update($user->id, ['last_login' => date('Y-m-d H:i:s')]);
            
            return true;
        }
        
        return false;
    }
    
    public function logout()
    {
        $this->session->destroy();
        return true;
    }
    
    public function isLoggedIn()
    {
        return $this->session->get('is_logged_in') === true;
    }
    
    public function isDakoiiUser()
    {
        return $this->session->get('user_type') === 'dakoii';
    }
    
    public function isOrgUser()
    {
        return $this->session->get('user_type') === 'organization';
    }
}

// app/Filters/DakoiiAuthFilter.php
<?php
namespace App\Filters;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;

class DakoiiAuthFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $session = \Config\Services::session();
        $auth = new \App\Libraries\AuthLibrary();
        
        if (!$auth->isLoggedIn() || !$auth->isDakoiiUser()) {
            return redirect()->to('/dakoii/login');
        }
    }
    
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Do nothing
    }
}
```

---

## 5. Frontend Design with Bootstrap 5

### 5.1 Layout Structure

```php
<!-- app/Views/layouts/dakoii_layout.php -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Dakoii Portal' ?> - Wanspeen</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= base_url('assets/css/dakoii.css') ?>" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="<?= base_url('dakoii') ?>">
                <i class="bi bi-shield-check"></i> Dakoii Portal
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('dakoii/dashboard') ?>">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('dakoii/organizations') ?>">
                            <i class="bi bi-building"></i> Organizations
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('dakoii/org-users') ?>">
                            <i class="bi bi-people"></i> Org Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('dakoii/product-types') ?>">
                            <i class="bi bi-tags"></i> Product Types
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?= session('full_name') ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?= base_url('dakoii/profile') ?>">Profile</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('dakoii/settings') ?>">Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= base_url('dakoii/logout') ?>">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="bi bi-list"></i> Quick Actions</h6>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="<?= base_url('dakoii/organizations/create') ?>" class="list-group-item list-group-item-action">
                            <i class="bi bi-plus-circle"></i> Add Organization
                        </a>
                        <a href="<?= base_url('dakoii/org-users/create') ?>" class="list-group-item list-group-item-action">
                            <i class="bi bi-person-plus"></i> Add Org User
                        </a>
                        <a href="<?= base_url('dakoii/product-types/create') ?>" class="list-group-item list-group-item-action">
                            <i class="bi bi-tag"></i> Add Product Type
                        </a>
                        <a href="<?= base_url('dakoii/reports') ?>" class="list-group-item list-group-item-action">
                            <i class="bi bi-graph-up"></i> View Reports
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Content Area -->
            <div class="col-md-9 col-lg-10">
                <!-- Breadcrumb -->
                <?php if (isset($breadcrumbs)): ?>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <?php foreach ($breadcrumbs as $breadcrumb): ?>
                            <?php if (isset($breadcrumb['url'])): ?>
                                <li class="breadcrumb-item">
                                    <a href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['title'] ?></a>
                                </li>
                            <?php else: ?>
                                <li class="breadcrumb-item active" aria-current="page"><?= $breadcrumb['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </nav>
                <?php endif; ?>

                <!-- Alert Messages -->
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle"></i> <?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle"></i> <?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Page Content -->
                <?= $this->renderSection('content') ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white mt-5 py-4">
        <div class="container text-center">
            <p>&copy; <?= date('Y') ?> Wanspeen Tourism System. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?= base_url('assets/js/dakoii.js') ?>"></script>
</body>
</html>

<!-- app/Views/layouts/organization_layout.php -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Organization Portal' ?> - Wanspeen</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= base_url('assets/css/organization.css') ?>" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="<?= base_url('organization') ?>">
                <i class="bi bi-geo-alt"></i> <?= session('organization_name') ?? 'Organization Portal' ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('organization/dashboard') ?>">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('organization/smes') ?>">
                            <i class="bi bi-shop"></i> SMEs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('organization/products') ?>">
                            <i class="bi bi-box"></i> Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('organization/events') ?>">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url() ?>" target="_blank">
                            <i class="bi bi-eye"></i> View Public Site
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?= session('full_name') ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?= base_url('organization/profile') ?>">Profile</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('organization/settings') ?>">Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= base_url('organization/logout') ?>">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h6>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="<?= base_url('organization/smes/create') ?>" class="list-group-item list-group-item-action">
                            <i class="bi bi-plus-circle"></i> Add SME
                        </a>
                        <a href="<?= base_url('organization/products/create') ?>" class="list-group-item list-group-item-action">
                            <i class="bi bi-box"></i> Add Product
                        </a>
                        <a href="<?= base_url('organization/events/create') ?>" class="list-group-item list-group-item-action">
                            <i class="bi bi-calendar-plus"></i> Add Event
                        </a>
                        <a href="<?= base_url('organization/reports') ?>" class="list-group-item list-group-item-action">
                            <i class="bi bi-bar-chart"></i> View Reports
                        </a>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="card mt-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="bi bi-graph-up"></i> Quick Stats</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Active SMEs:</span>
                            <span class="badge bg-primary"><?= $stats['sme_count'] ?? 0 ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Published Products:</span>
                            <span class="badge bg-success"><?= $stats['product_count'] ?? 0 ?></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Upcoming Events:</span>
                            <span class="badge bg-info"><?= $stats['event_count'] ?? 0 ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content Area -->
            <div class="col-md-9 col-lg-10">
                <!-- Breadcrumb -->
                <?php if (isset($breadcrumbs)): ?>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <?php foreach ($breadcrumbs as $breadcrumb): ?>
                            <?php if (isset($breadcrumb['url'])): ?>
                                <li class="breadcrumb-item">
                                    <a href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['title'] ?></a>
                                </li>
                            <?php else: ?>
                                <li class="breadcrumb-item active" aria-current="page"><?= $breadcrumb['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </nav>
                <?php endif; ?>

                <!-- Alert Messages -->
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle"></i> <?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle"></i> <?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Page Content -->
                <?= $this->renderSection('content') ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white mt-5 py-4">
        <div class="container text-center">
            <p>&copy; <?= date('Y') ?> Wanspeen Tourism System. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?= base_url('assets/js/organization.js') ?>"></script>
</body>
</html>
```

### 5.2 Sample Views

```php
<!-- app/Views/dakoii/dashboard.php -->
<?= $this->extend('layouts/dakoii_layout') ?>
<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0"><i class="bi bi-speedometer2"></i> Dashboard</h1>
    <div class="btn-group" role="group">
        <a href="<?= base_url('dakoii/organizations/create') ?>" class="btn btn-primary">
            <i class="bi bi-plus"></i> Add Organization
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Organizations</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $total_organizations ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-building fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Admins</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $total_admins ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Product Types</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $total_product_types ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-tags fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">System Health</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">100%</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-heart-pulse fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Organizations -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">Recent Organizations</h6>
        <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-sm btn-outline-primary">View All</a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Code</th>
                        <th>Name</th>
                        <th>Contact Person</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_organizations as $org): ?>
                    <tr>
                        <td><span class="badge bg-secondary"><?= $org->code ?></span></td>
                        <td><?= $org->name ?></td>
                        <td><?= $org->contact_person ?></td>
                        <td><?= $org->email ?></td>
                        <td>
                            <?php if ($org->is_active): ?>
                                <span class="badge bg-success">Active</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Inactive</span>
                            <?php endif; ?>
                        </td>
                        <td><?= date('M d, Y', strtotime($org->created_at)) ?></td>
                        <td>
                            <a href="<?= base_url('dakoii/organizations/view/' . $org->id) ?>" class="btn btn-sm btn-outline-info">
                                <i class="bi bi-eye"></i>
                            </a>
                            <a href="<?= base_url('dakoii/organizations/edit/' . $org->id) ?>" class="btn btn-sm btn-outline-warning">
                                <i class="bi bi-pencil"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<!-- app/Views/organization/products/index.php -->
<?= $this->extend('layouts/organization_layout') ?>
<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0"><i class="bi bi-box"></i> Tourism Products</h1>
    <div class="btn-group" role="group">
        <a href="<?= base_url('organization/products/create') ?>" class="btn btn-primary">
            <i class="bi bi-plus"></i> Add Product
        </a>
        <a href="<?= base_url('organization/products/import') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-upload"></i> Import
        </a>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= base_url('organization/products') ?>">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="sme_id" class="form-label">SME</label>
                    <select class="form-select" name="sme_id" id="sme_id">
                        <option value="">All SMEs</option>
                        <?php foreach ($smes as $sme): ?>
                            <option value="<?= $sme->id ?>" <?= ($filters['sme_id'] ?? '') == $sme->id ? 'selected' : '' ?>>
                                <?= $sme->business_name ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="product_type_id" class="form-label">Product Type</label>
                    <select class="form-select" name="product_type_id" id="product_type_id">
                        <option value="">All Types</option>
                        <?php foreach ($product_types as $type): ?>
                            <option value="<?= $type->id ?>" <?= ($filters['product_type_id'] ?? '') == $type->id ? 'selected' : '' ?>>
                                <?= $type->name ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" name="status" id="status">
                        <option value="">All Statuses</option>
                        <option value="draft" <?= ($filters['status'] ?? '') == 'draft' ? 'selected' : '' ?>>Draft</option>
                        <option value="pending_review" <?= ($filters['status'] ?? '') == 'pending_review' ? 'selected' : '' ?>>Pending Review</option>
                        <option value="published" <?= ($filters['status'] ?? '') == 'published' ? 'selected' : '' ?>>Published</option>
                        <option value="suspended" <?= ($filters['status'] ?? '') == 'suspended' ? 'selected' : '' ?>>Suspended</option>
                        <option value="archived" <?= ($filters['status'] ?? '') == 'archived' ? 'selected' : '' ?>>Archived</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="bi bi-search"></i> Filter
                    </button>
                    <a href="<?= base_url('organization/products') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle"></i> Clear
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Products Table -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Products List</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Image</th>
                        <th>Name</th>
                        <th>SME</th>
                        <th>Type</th>
                        <th>Price</th>
                        <th>Status</th>
                        <th>Views</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($products as $product): ?>
                    <tr>
                        <td>
                            <?php if ($product->featured_image_path): ?>
                                <img src="<?= base_url('uploads/' . $product->featured_image_path) ?>" 
                                     alt="<?= $product->name ?>" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                    <i class="bi bi-image text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <strong><?= $product->name ?></strong><br>
                            <small class="text-muted"><?= $product->code ?></small>
                        </td>
                        <td><?= $product->sme_name ?></td>
                        <td><span class="badge bg-info"><?= $product->type_name ?></span></td>
                        <td>
                            <?php if ($product->price_adult): ?>
                                <?= $product->currency ?> <?= number_format($product->price_adult, 2) ?>
                            <?php else: ?>
                                <span class="text-muted">N/A</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $statusColors = [
                                'draft' => 'secondary',
                                'pending_review' => 'warning',
                                'published' => 'success',
                                'suspended' => 'danger',
                                'archived' => 'dark'
                            ];
                            ?>
                            <span class="badge bg-<?= $statusColors[$product->status] ?? 'secondary' ?>">
                                <?= ucfirst(str_replace('_', ' ', $product->status)) ?>
                            </span>
                        </td>
                        <td><?= number_format($product->view_count) ?></td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="<?= base_url('organization/products/view/' . $product->id) ?>" 
                                   class="btn btn-outline-info" title="View">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="<?= base_url('organization/products/edit/' . $product->id) ?>" 
                                   class="btn btn-outline-warning" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="<?= base_url('organization/products/clone/' . $product->id) ?>" 
                                   class="btn btn-outline-secondary" title="Clone">
                                    <i class="bi bi-files"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="confirmDelete(<?= $product->id ?>, '<?= $product->name ?>')" title="Delete">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($pager): ?>
            <div class="d-flex justify-content-center mt-4">
                <?= $pager->links() ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the product "<span id="productName"></span>"?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(productId, productName) {
    document.getElementById('productName').textContent = productName;
    document.getElementById('confirmDeleteBtn').onclick = function() {
        window.location.href = '<?= base_url("organization/products/delete/") ?>' + productId;
    };
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?= $this->endSection() ?>
```

---

## 5. API Routes Configuration

```php
// app/Config/Routes.php
<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Public Tourism Site Routes
$routes->get('/', 'Public\Home::index');
$routes->get('products', 'Public\Products::index');
$routes->get('products/(:segment)', 'Public\Products::view/$1');
$routes->get('events', 'Public\Events::index');
$routes->get('events/(:segment)', 'Public\Events::view/$1');
$routes->get('destinations', 'Public\Destinations::index');
$routes->get('search', 'Public\Search::index');

// Dakoii Portal Routes (Super Admin)
$routes->group('dakoii', ['filter' => 'dakoii_auth'], function($routes) {
    $routes->get('/', 'Dakoii\Dashboard::index');
    $routes->get('dashboard', 'Dakoii\Dashboard::index');
    
    // Organizations Management
    $routes->group('organizations', function($routes) {
        $routes->get('/', 'Dakoii\Organizations::index');
        $routes->get('create', 'Dakoii\Organizations::create');
        $routes->post('store', 'Dakoii\Organizations::store');
        $routes->get('view/(:num)', 'Dakoii\Organizations::view/$1');
        $routes->get('edit/(:num)', 'Dakoii\Organizations::edit/$1');
        $routes->post('update/(:num)', 'Dakoii\Organizations::update/$1');
        $routes->get('delete/(:num)', 'Dakoii\Organizations::delete/$1');
        $routes->post('toggle-status/(:num)', 'Dakoii\Organizations::toggleStatus/$1');
    });
    
    // Organization Users Management
    $routes->group('org-users', function($routes) {
        $routes->get('/', 'Dakoii\OrgUsers::index');
        $routes->get('create', 'Dakoii\OrgUsers::create');
        $routes->post('store', 'Dakoii\OrgUsers::store');
        $routes->get('view/(:num)', 'Dakoii\OrgUsers::view/$1');
        $routes->get('edit/(:num)', 'Dakoii\OrgUsers::edit/$1');
        $routes->post('update/(:num)', 'Dakoii\OrgUsers::update/$1');
        $routes->get('delete/(:num)', 'Dakoii\OrgUsers::delete/$1');
        $routes->post('reset-password/(:num)', 'Dakoii\OrgUsers::resetPassword/$1');
    });
    
    // Product Types Management
    $routes->group('product-types', function($routes) {
        $routes->get('/', 'Dakoii\ProductTypes::index');
        $routes->get('create', 'Dakoii\ProductTypes::create');
        $routes->post('store', 'Dakoii\ProductTypes::store');
        $routes->get('edit/(:num)', 'Dakoii\ProductTypes::edit/$1');
        $routes->post('update/(:num)', 'Dakoii\ProductTypes::update/$1');
        $routes->get('delete/(:num)', 'Dakoii\ProductTypes::delete/$1');
        $routes->post('reorder', 'Dakoii\ProductTypes::reorder');
    });
    
    // Reports and Analytics
    $routes->get('reports', 'Dakoii\Reports::index');
    $routes->get('reports/organizations', 'Dakoii\Reports::organizations');
    $routes->get('reports/usage', 'Dakoii\Reports::usage');
    
    // Settings
    $routes->get('settings', 'Dakoii\Settings::index');
    $routes->post('settings/update', 'Dakoii\Settings::update');
    
    // Profile Management
    $routes->get('profile', 'Dakoii\Profile::index');
    $routes->post('profile/update', 'Dakoii\Profile::update');
});

// Dakoii Authentication Routes (No Auth Filter)
$routes->group('dakoii', function($routes) {
    $routes->get('login', 'Dakoii\Auth::login');
    $routes->post('authenticate', 'Dakoii\Auth::authenticate');
    $routes->get('logout', 'Dakoii\Auth::logout');
    $routes->get('forgot-password', 'Dakoii\Auth::forgotPassword');
    $routes->post('reset-password', 'Dakoii\Auth::resetPassword');
});

// Organization Portal Routes
$routes->group('organization', ['filter' => 'org_auth'], function($routes) {
    $routes->get('/', 'Organization\Dashboard::index');
    $routes->get('dashboard', 'Organization\Dashboard::index');
    
    // SMEs Management
    $routes->group('smes', function($routes) {
        $routes->get('/', 'Organization\Smes::index');
        $routes->get('create', 'Organization\Smes::create');
        $routes->post('store', 'Organization\Smes::store');
        $routes->get('view/(:num)', 'Organization\Smes::view/$1');
        $routes->get('edit/(:num)', 'Organization\Smes::edit/$1');
        $routes->post('update/(:num)', 'Organization\Smes::update/$1');
        $routes->get('delete/(:num)', 'Organization\Smes::delete/$1');
        $routes->post('toggle-status/(:num)', 'Organization\Smes::toggleStatus/$1');
        $routes->post('toggle-featured/(:num)', 'Organization\Smes::toggleFeatured/$1');
    });
    
    // Products Management
    $routes->group('products', function($routes) {
        $routes->get('/', 'Organization\Products::index');
        $routes->get('create', 'Organization\Products::create');
        $routes->post('store', 'Organization\Products::store');
        $routes->get('view/(:num)', 'Organization\Products::view/$1');
        $routes->get('edit/(:num)', 'Organization\Products::edit/$1');
        $routes->post('update/(:num)', 'Organization\Products::update/$1');
        $routes->get('delete/(:num)', 'Organization\Products::delete/$1');
        $routes->get('clone/(:num)', 'Organization\Products::clone/$1');
        $routes->post('change-status/(:num)', 'Organization\Products::changeStatus/$1');
        $routes->post('toggle-featured/(:num)', 'Organization\Products::toggleFeatured/$1');
        
        // File Management
        $routes->post('upload-image/(:num)', 'Organization\Products::uploadImage/$1');
        $routes->get('delete-image/(:num)', 'Organization\Products::deleteImage/$1');
        
        // Import/Export
        $routes->get('import', 'Organization\Products::import');
        $routes->post('process-import', 'Organization\Products::processImport');
        $routes->get('export', 'Organization\Products::export');
    });
    
    // Events Management
    $routes->group('events', function($routes) {
        $routes->get('/', 'Organization\Events::index');
        $routes->get('create', 'Organization\Events::create');
        $routes->post('store', 'Organization\Events::store');
        $routes->get('view/(:num)', 'Organization\Events::view/$1');
        $routes->get('edit/(:num)', 'Organization\Events::edit/$1');
        $routes->post('update/(:num)', 'Organization\Events::update/$1');
        $routes->get('delete/(:num)', 'Organization\Events::delete/$1');
        $routes->get('clone/(:num)', 'Organization\Events::clone/$1');
        $routes->post('change-status/(:num)', 'Organization\Events::changeStatus/$1');
        $routes->post('toggle-featured/(:num)', 'Organization\Events::toggleFeatured/$1');
        
        // Calendar View
        $routes->get('calendar', 'Organization\Events::calendar');
        $routes->get('calendar-data', 'Organization\Events::calendarData');
    });
    
    // Reports and Analytics
    $routes->get('reports', 'Organization\Reports::index');
    $routes->get('reports/products', 'Organization\Reports::products');
    $routes->get('reports/events', 'Organization\Reports::events');
    $routes->get('reports/smes', 'Organization\Reports::smes');
    
    // Profile Management
    $routes->get('profile', 'Organization\Profile::index');
    $routes->post('profile/update', 'Organization\Profile::update');
    $routes->post('profile/change-password', 'Organization\Profile::changePassword');
});

// Organization Authentication Routes (No Auth Filter)
$routes->group('organization', function($routes) {
    $routes->get('login', 'Organization\Auth::login');
    $routes->post('authenticate', 'Organization\Auth::authenticate');
    $routes->get('logout', 'Organization\Auth::logout');
    $routes->get('forgot-password', 'Organization\Auth::forgotPassword');
    $routes->post('reset-password', 'Organization\Auth::resetPassword');
});

// API Routes for AJAX calls
$routes->group('api', function($routes) {
    // Public API
    $routes->get('products/search', 'Api\Products::search');
    $routes->get('events/search', 'Api\Events::search');
    $routes->get('locations', 'Api\Locations::index');
    
    // Authenticated API
    $routes->group('organization', ['filter' => 'org_auth'], function($routes) {
        $routes->get('smes/list', 'Api\Organization\Smes::list');
        $routes->get('products/list', 'Api\Organization\Products::list');
        $routes->get('product-types/list', 'Api\Organization\ProductTypes::list');
    });
    
    $routes->group('dakoii', ['filter' => 'dakoii_auth'], function($routes) {
        $routes->get('organizations/list', 'Api\Dakoii\Organizations::list');
        $routes->get('stats/overview', 'Api\Dakoii\Stats::overview');
    });
});
```

---

## 6. File Upload and Media Management

```php
// app/Libraries/FileUploadLibrary.php
<?php
namespace App\Libraries;

class FileUploadLibrary
{
    protected $allowedTypes = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'document' => ['pdf', 'doc', 'docx', 'txt'],
        'video' => ['mp4', 'avi', 'mov', 'wmv']
    ];
    
    protected $maxSizes = [
        'image' => 5 * 1024 * 1024, // 5MB
        'document' => 10 * 1024 * 1024, // 10MB
        'video' => 50 * 1024 * 1024 // 50MB
    ];
    
    public function uploadFile($file, $entityType, $entityId, $category = 'general')
    {
        if (!$file->isValid()) {
            throw new \Exception('Invalid file upload');
        }
        
        $fileType = $this->getFileType($file->getExtension());
        
        if (!$this->isAllowedType($file->getExtension(), $fileType)) {
            throw new \Exception('File type not allowed');
        }
        
        if ($file->getSize() > $this->maxSizes[$fileType]) {
            throw new \Exception('File size exceeds limit');
        }
        
        $uploadPath = $this->getUploadPath($entityType, $entityId);
        $fileName = $this->generateFileName($file);
        
        // Ensure directory exists
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }
        
        // Move file
        if ($file->move($uploadPath, $fileName)) {
            // Save file record
            $fileUploadModel = new \App\Models\FileUploadModel();
            $fileRecord = [
                'entity_type' => $entityType,
                'entity_id' => $entityId,
                'file_category' => $category,
                'original_name' => $file->getName(),
                'stored_name' => $fileName,
                'file_path' => $uploadPath . '/' . $fileName,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'uploaded_by_type' => session('user_type'),
                'uploaded_by_id' => session('user_id')
            ];
            
            $fileId = $fileUploadModel->insert($fileRecord);
            
            return [
                'id' => $fileId,
                'path' => $uploadPath . '/' . $fileName,
                'url' => base_url('uploads/' . $entityType . '/' . $entityId . '/' . $fileName),
                'name' => $fileName,
                'size' => $file->getSize()
            ];
        }
        
        throw new \Exception('Failed to upload file');
    }
    
    protected function getUploadPath($entityType, $entityId)
    {
        return WRITEPATH . 'uploads/' . $entityType . '/' . $entityId;
    }
    
    protected function generateFileName($file)
    {
        return time() . '_' . uniqid() . '.' . $file->getExtension();
    }
    
    protected function getFileType($extension)
    {
        foreach ($this->allowedTypes as $type => $extensions) {
            if (in_array(strtolower($extension), $extensions)) {
                return $type;
            }
        }
        return 'unknown';
    }
    
    protected function isAllowedType($extension, $type)
    {
        return in_array(strtolower($extension), $this->allowedTypes[$type] ?? []);
    }
    
    public function deleteFile($filePath)
    {
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        return false;
    }
    
    public function resizeImage($imagePath, $width, $height, $maintainRatio = true)
    {
        $imageService = \Config\Services::image();
        
        try {
            $imageService->withFile($imagePath);
            
            if ($maintainRatio) {
                $imageService->fit($width, $height, 'center');
            } else {
                $imageService->resize($width, $height);
            }
            
            $imageService->save($imagePath);
            return true;
        } catch (\Exception $e) {
            log_message('error', 'Image resize failed: ' . $e->getMessage());
            return false;
        }
    }
}

// app/Helpers/upload_helper.php
<?php

if (!function_exists('upload_file')) {
    function upload_file($file, $entityType, $entityId, $category = 'general')
    {
        $uploadLib = new \App\Libraries\FileUploadLibrary();
        return $uploadLib->uploadFile($file, $entityType, $entityId, $category);
    }
}

if (!function_exists('get_file_url')) {
    function get_file_url($filePath)
    {
        if (empty($filePath)) {
            return null;
        }
        
        // If it's already a full URL, return as is
        if (filter_var($filePath, FILTER_VALIDATE_URL)) {
            return $filePath;
        }
        
        // If it starts with uploads/, return with base_url
        if (strpos($filePath, 'uploads/') === 0) {
            return base_url($filePath);
        }
        
        // Otherwise, assume it's in uploads directory
        return base_url('uploads/' . $filePath);
    }
}

if (!function_exists('format_file_size')) {
    function format_file_size($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
```

---

## 7. Security Implementation

```php
// app/Filters/SecurityFilter.php
<?php
namespace App\Filters;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;

class SecurityFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // CSRF Protection for POST requests
        if ($request->getMethod() === 'POST') {
            $csrf = \Config\Services::security();
            if (!$csrf->verify($request)) {
                throw new \CodeIgniter\Security\Exceptions\SecurityException('CSRF token mismatch');
            }
        }
        
        // Rate limiting
        $this->checkRateLimit($request);
        
        // Input sanitization
        $this->sanitizeInput($request);
    }
    
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Add security headers
        $response->setHeader('X-Content-Type-Options', 'nosniff');
        $response->setHeader('X-Frame-Options', 'DENY');
        $response->setHeader('X-XSS-Protection', '1; mode=block');
        $response->setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        
        return $response;
    }
    
    protected function checkRateLimit($request)
    {
        $cache = \Config\Services::cache();
        $ip = $request->getIPAddress();
        $key = 'rate_limit_' . $ip;
        
        $attempts = $cache->get($key) ?? 0;
        
        if ($attempts >= 100) { // 100 requests per hour
            throw new \Exception('Rate limit exceeded', 429);
        }
        
        $cache->save($key, $attempts + 1, 3600); // 1 hour
    }
    
    protected function sanitizeInput($request)
    {
        // Additional input sanitization if needed
        $input = $request->getVar();
        
        foreach ($input as $key => $value) {
            if (is_string($value)) {
                // Remove potential XSS
                $value = strip_tags($value);
                // Remove SQL injection attempts
                $value = preg_replace('/(\bselect\b|\binsert\b|\bupdate\b|\bdelete\b|\bunion\b)/i', '', $value);
            }
        }
    }
}

// app/Libraries/PermissionLibrary.php
<?php
namespace App\Libraries;

class PermissionLibrary
{
    protected $permissions = [
        'organization' => [
            'admin' => [
                'smes' => ['create', 'read', 'update', 'delete'],
                'products' => ['create', 'read', 'update', 'delete', 'publish'],
                'events' => ['create', 'read', 'update', 'delete', 'publish'],
                'users' => ['create', 'read', 'update', 'delete'],
                'reports' => ['read']
            ],
            'manager' => [
                'smes' => ['create', 'read', 'update'],
                'products' => ['create', 'read', 'update'],
                'events' => ['create', 'read', 'update'],
                'reports' => ['read']
            ],
            'editor' => [
                'smes' => ['read'],
                'products' => ['create', 'read', 'update'],
                'events' => ['create', 'read', 'update']
            ]
        ]
    ];
    
    public function hasPermission($resource, $action, $userType = null, $role = null)
    {
        $session = session();
        
        if (!$userType) {
            $userType = $session->get('user_type');
        }
        
        if (!$role) {
            $role = $session->get('role');
        }
        
        // Dakoii users have all permissions
        if ($userType === 'dakoii') {
            return true;
        }
        
        // Check organization user permissions
        if ($userType === 'organization') {
            $userPermissions = $this->permissions['organization'][$role] ?? [];
            return in_array($action, $userPermissions[$resource] ?? []);
        }
        
        return false;
    }
    
    public function requirePermission($resource, $action)
    {
        if (!$this->hasPermission($resource, $action)) {
            throw new \Exception('Access denied: Insufficient permissions', 403);
        }
    }
    
    public function getUserPermissions($userType, $role)
    {
        if ($userType === 'dakoii') {
            return 'all';
        }
        
        return $this->permissions['organization'][$role] ?? [];
    }
}
```

---

## 8. Configuration Files

```php
// app/Config/App.php additions
<?php
// Add these to the existing App.php file

public $wanspeen = [
    'upload_path' => WRITEPATH . 'uploads/',
    'max_file_size' => 10 * 1024 * 1024, // 10MB
    'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
    'image_sizes' => [
        'thumbnail' => [150, 150],
        'medium' => [300, 300],
        'large' => [800, 600]
    ],
    'pagination' => [
        'per_page' => 20,
        'per_page_options' => [10, 20, 50, 100]
    ],
    'currencies' => ['PGK', 'USD', 'AUD'],
    'default_currency' => 'PGK',
    'timezone' => 'Pacific/Port_Moresby'
];

// app/Config/Database.php
<?php
namespace Config;

use CodeIgniter\Database\Config;

class Database extends Config
{
    public $default = [
        'DSN'      => '',
        'hostname' => 'localhost',
        'username' => 'wanspeen_user',
        'password' => 'secure_password',
        'database' => 'wanspeen_db',
        'DBDriver' => 'MySQLi',
        'DBPrefix' => '',
        'pConnect' => false,
        'DBDebug'  => (ENVIRONMENT !== 'production'),
        'charset'  => 'utf8mb4',
        'DBCollat' => 'utf8mb4_unicode_ci',
        'swapPre'  => '',
        'encrypt'  => false,
        'compress' => false,
        'strictOn' => false,
        'failover' => [],
        'port'     => 3306,
    ];
}

// app/Config/Validation.php additions
<?php
// Add custom validation rules

public $wanspeen_rules = [
    'organization_code' => [
        'label' => 'Organization Code',
        'rules' => 'required|min_length[3]|max_length[20]|alpha_numeric|is_unique[organizations.code,id,{id}]'
    ],
    'product_code' => [
        'label' => 'Product Code',
        'rules' => 'required|min_length[3]|max_length[50]|alpha_numeric_dash'
    ],
    'sme_code' => [
        'label' => 'SME Code',
        'rules' => 'required|min_length[3]|max_length[50]|alpha_numeric_dash'
    ],
    'event_code' => [
        'label' => 'Event Code',
        'rules' => 'required|min_length[3]|max_length[50]|alpha_numeric_dash'
    ],
    'price' => [
        'label' => 'Price',
        'rules' => 'permit_empty|decimal|greater_than_equal_to[0]'
    ],
    'image_upload' => [
        'label' => 'Image',
        'rules' => 'uploaded[image]|is_image[image]|max_size[image,5120]'
    ]
];
```

---

## 9. Deployment Configuration

```php
// app/Config/Environment.php
<?php

// Production Environment Settings
if (ENVIRONMENT === 'production') {
    // Database Configuration
    $_ENV['database.default.hostname'] = 'your_production_db_host';
    $_ENV['database.default.username'] = 'your_production_db_user';
    $_ENV['database.default.password'] = 'your_production_db_password';
    $_ENV['database.default.database'] = 'wanspeen_production';
    
    // Security Settings
    $_ENV['encryption.key'] = 'your_32_character_encryption_key_here';
    $_ENV['app.sessionCookieName'] = 'wanspeen_session';
    $_ENV['app.sessionExpiration'] = 7200;
    
    // File Upload Settings
    $_ENV['app.maxFileSize'] = '10M';
    $_ENV['app.uploadPath'] = '/var/www/uploads/';
    
    // Email Configuration
    $_ENV['email.SMTPHost'] = 'your_smtp_host';
    $_ENV['email.SMTPUser'] = 'your_smtp_user';
    $_ENV['email.SMTPPass'] = 'your_smtp_password';
    $_ENV['email.SMTPPort'] = 587;
    $_ENV['email.mailType'] = 'html';
}

// .htaccess for Apache
# RewriteEngine On
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^(.*)$ index.php/$1 [L]

# Security Headers
# Header always set X-Content-Type-Options nosniff
# Header always set X-Frame-Options DENY
# Header always set X-XSS-Protection "1; mode=block"

# File Upload Security
# <FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
#     Order Deny,Allow
#     Deny from all
# </FilesMatch>
```

This comprehensive system design provides a robust foundation for the Wanspeen Tourism Product Information System. The design includes detailed database schemas, CodeIgniter 4 implementation structure, Bootstrap 5 frontend components, security measures, file upload handling, and deployment configurations.

Key features implemented:
- Complete dual-portal architecture
- Comprehensive CRUD operations for all entities
- Role-based authentication and authorization
- File upload and media management
- Responsive Bootstrap 5 interface
- Security best practices
- API endpoints for AJAX functionality
- Comprehensive validation and error handling
- Activity logging and audit trails
- Scalable database design with proper indexing

The system is designed to be maintainable, secure, and scalable for tourism organizations in Papua New Guinea and beyond.