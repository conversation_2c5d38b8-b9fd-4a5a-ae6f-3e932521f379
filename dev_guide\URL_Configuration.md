# Wanspeen URL Configuration Guide

## Base URL Configuration

The Wanspeen application uses CodeIgniter 4's `base_url()` function throughout the application to ensure proper URL generation across different environments.

### Current Configuration

**File**: `app/Config/App.php`
```php
public string $baseURL = 'http://localhost:8080/';
```

**Environment File**: `.env`
```
app.baseURL = 'http://localhost:8080/'
```

## Environment-Specific URLs

### 1. Development Environment (CodeIgniter Spark)
```bash
php spark serve
```
- **URL**: `http://localhost:8080/`
- **Usage**: Primary development server
- **Features**: Hot reload, debugging enabled

### 2. XAMPP Environment
```
app.baseURL = 'http://localhost/wanspeen/'
```
- **URL**: `http://localhost/wanspeen/`
- **Usage**: Local Apache server testing
- **Setup**: Place project in `htdocs/wanspeen/`

### 3. Production Environment
```
app.baseURL = 'https://wanspeen.com/'
```
- **URL**: `https://wanspeen.com/`
- **Usage**: Live production server
- **Features**: HTTPS, optimized for performance

## URL Structure

### Public Routes
- **Landing Page**: `{baseURL}/`
- **About**: `{baseURL}/#about`
- **Features**: `{baseURL}/#features`
- **Contact**: `{baseURL}/#contact`

### Portal Routes
- **Organization Portal**: `{baseURL}/organization`
- **Dakoii Portal**: `{baseURL}/dakoii`
- **API Endpoints**: `{baseURL}/api/v1/`

### Asset URLs
All assets use `base_url()` for proper path resolution:
- **CSS**: `{baseURL}/assets/css/`
- **JavaScript**: `{baseURL}/assets/js/`
- **Images**: `{baseURL}/assets/images/`

## Implementation Examples

### In Views
```php
<!-- Navigation brand link -->
<a href="<?= base_url() ?>">Wanspeen</a>

<!-- Portal links -->
<a href="<?= base_url('organization') ?>">Organization Portal</a>

<!-- Asset links -->
<link href="<?= base_url('assets/css/wanspeen.css') ?>" rel="stylesheet">
```

### In Controllers
```php
// Redirect to home
return redirect()->to(base_url());

// Redirect to specific route
return redirect()->to(base_url('organization/dashboard'));
```

### In JavaScript
```javascript
// AJAX requests
fetch(baseURL + 'api/v1/products')

// Dynamic redirects
window.location.href = baseURL + 'organization';
```

## Configuration Steps

### For Development
1. Use CodeIgniter's built-in server:
   ```bash
   php spark serve
   ```
2. Access: `http://localhost:8080/`

### For XAMPP Testing (Current Active)
1. Update `.env`:
   ```
   app.baseURL = 'http://localhost/wanspeen/'
   ```
2. Access: `http://localhost/wanspeen/`
3. Ensure XAMPP Apache is running

### For Production
1. Update `.env`:
   ```
   app.baseURL = 'https://wanspeen.com/'
   CI_ENVIRONMENT = production
   ```
2. Configure web server (Apache/Nginx)
3. Set up SSL certificate

## URL Rewriting

The application is configured to remove `index.php` from URLs:

### Before
```
http://localhost:8080/index.php/organization
```

### After
```
http://localhost:8080/organization
```

This is achieved through:
1. Moving `index.php` to root directory
2. Proper `.htaccess` configuration (for Apache)
3. CodeIgniter 4 routing configuration

## Security Considerations

### Production URLs
- Always use HTTPS in production
- Configure proper SSL certificates
- Set `forceGlobalSecureRequests = true` in production

### Environment Variables
- Never commit `.env` files to version control
- Use different `.env` files for different environments
- Keep sensitive data in environment variables

## Testing URLs

### Manual Testing
```bash
# Test landing page
curl http://localhost:8080/

# Test with headers
curl -H "Accept: application/json" http://localhost:8080/api/v1/test
```

### Automated Testing
```php
// In tests
$this->get(base_url());
$this->assertStatus(200);
```

## Troubleshooting

### Common Issues
1. **404 Errors**: Check route configuration in `app/Config/Routes.php`
2. **Asset Loading**: Verify `base_url()` usage in views
3. **CORS Issues**: Configure proper headers for API endpoints
4. **SSL Issues**: Check certificate configuration in production

### Debug Commands
```bash
# Check routes
php spark routes

# Check environment
php spark env

# Clear cache
php spark cache:clear
```
